---
inclusion: always
---

# Uniteam UI Project Guidelines

## Command Execution
- Always verify the current working directory before executing commands
- If not already in the `uniteam-ui` directory, navigate to it first using `cd uniteam-ui`
- Execute commands directly without unnecessary flags or parameters unless specifically required
- Ensure all npm scripts are run from the project root directory

## Project Structure
- The project is a TypeScript-based UI application using Vite as the build tool
- Follow the existing folder structure for new components and features
- Keep related files grouped together for better organization

## Code Style
- Use TypeScript for all new code
- Follow consistent naming conventions (camelCase for variables, PascalCase for components)
- Maintain clean code practices with proper indentation and formatting

## Best Practices
- Verify commands are compatible with the Windows environment
- Use relative imports for project files
- Document complex logic with clear comments
- Follow the existing architectural patterns when implementing new features

## Tailwind v4 reference
- https://tailwindcss.com/blog/tailwindcss-v4