@import url('https://fonts.googleapis.com/css2?family=Manrope:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

@theme {
  --font-family-sans: 'Manrope', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  
  /* Primary Colors - Uniteam Brand */
  --color-primary-50: #F1F1F1;
  --color-primary-100: #D2EFFF;
  --color-primary-200: #A7E1FF;
  --color-primary-300: #7CCDFD;
  --color-primary-400: #0B9FF4;
  --color-primary-500: #0B9FF4;
  --color-primary-600: #0069A5;
  --color-primary-700: #005A8F;
  --color-primary-800: #004B79;
  --color-primary-900: #003C63;
  --color-primary-950: #202020;
  
  /* Secondary Colors */
  --color-secondary-50: #ecfdf5;
  --color-secondary-100: #d1fae5;
  --color-secondary-200: #a7f3d0;
  --color-secondary-300: #6ee7b7;
  --color-secondary-400: #34d399;
  --color-secondary-500: #10b981;
  --color-secondary-600: #059669;
  --color-secondary-700: #047857;
  --color-secondary-800: #065f46;
  --color-secondary-900: #064e3b;
  --color-secondary-950: #022c22;
  
  /* Neutral Colors - Uniteam Brand */
  --color-neutral-50: #F1F1F1;
  --color-neutral-100: #E8E8E8;
  --color-neutral-200: #D1D1D1;
  --color-neutral-300: #B8B8B8;
  --color-neutral-400: #9F9F9F;
  --color-neutral-500: #868686;
  --color-neutral-600: #6D6D6D;
  --color-neutral-700: #545454;
  --color-neutral-800: #3B3B3B;
  --color-neutral-900: #202020;
  --color-neutral-950: #0A0A0A;
  
  /* Shadows */
  --shadow-soft: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Configure dark mode for Tailwind CSS v4 */
@variant dark (.dark &);

/* Base styles */
html {
  font-family: var(--font-family-sans);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-family-sans);
  @apply bg-neutral-100 text-neutral-900 dark:bg-neutral-950 dark:text-neutral-50;
  @apply transition-colors duration-200;
}

/* Typography Classes */
.text-heading-1 {
  @apply text-3xl font-semibold leading-tight tracking-tight;
}

.text-heading-2 {
  @apply text-2xl font-semibold leading-tight tracking-tight;
}

.text-heading-3 {
  @apply text-xl font-medium leading-tight tracking-tight;
}

.text-body-lg {
  @apply text-lg leading-relaxed;
}

.text-body {
  @apply text-base leading-relaxed;
}

.text-body-sm {
  @apply text-sm leading-relaxed;
}

.text-caption {
  @apply text-xs leading-tight tracking-wide;
}

/* Component Classes */
.card {
  @apply bg-white dark:bg-neutral-800 rounded-xl border border-neutral-200 dark:border-neutral-700;
  @apply transition-colors duration-200;
  box-shadow: var(--shadow-soft);
}

.card-hover {
  @apply bg-white dark:bg-neutral-800 rounded-xl border border-neutral-200 dark:border-neutral-700;
  @apply hover:shadow-lg transition-all duration-200;
  box-shadow: var(--shadow-soft);
}

.btn-primary {
  @apply inline-flex items-center justify-center px-4 py-2 rounded-lg;
  @apply bg-primary-600 hover:bg-primary-700 active:bg-primary-800;
  @apply text-white font-medium text-sm;
  @apply transition-colors duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply inline-flex items-center justify-center px-4 py-2 rounded-lg;
  @apply bg-neutral-100 hover:bg-neutral-200 active:bg-neutral-300;
  @apply dark:bg-neutral-800 dark:hover:bg-neutral-700 dark:active:bg-neutral-600;
  @apply text-neutral-900 dark:text-neutral-100 font-medium text-sm;
  @apply transition-colors duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-neutral-500 focus:ring-offset-2;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-ghost {
  @apply inline-flex items-center justify-center px-4 py-2 rounded-lg;
  @apply bg-transparent hover:bg-neutral-100 active:bg-neutral-200;
  @apply dark:hover:bg-neutral-800 dark:active:bg-neutral-700;
  @apply text-neutral-700 dark:text-neutral-300 font-medium text-sm;
  @apply transition-colors duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-neutral-500 focus:ring-offset-2;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
}

.input {
  @apply w-full px-3 py-2 rounded-lg border border-neutral-300 dark:border-neutral-600;
  @apply bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100;
  @apply placeholder-neutral-500 dark:placeholder-neutral-400 text-sm;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  @apply transition-colors duration-200;
}

/* Utility Classes */
.text-muted {
  @apply text-neutral-600 dark:text-neutral-400;
}

.text-subtle {
  @apply text-neutral-500 dark:text-neutral-500;
}

.bg-subtle {
  @apply bg-neutral-50 dark:bg-neutral-900;
}